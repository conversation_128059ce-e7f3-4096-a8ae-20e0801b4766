/**
 * API迁移验证工具专用样式
 * 扩展基础组件库，提供工具特定的UI样式
 */

/* ========== 工具主界面 ========== */
.api-migration-validator-modal .modal-content {
  max-width: 1000px;
  width: 95vw;
  max-height: 90vh;
}

.api-migration-validator-modal .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.api-migration-validator-modal .header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* ========== 标签页内容 ========== */
.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* ========== 工具栏样式 ========== */
.rules-toolbar,
.reports-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
}

.rules-toolbar .btn,
.reports-toolbar .btn {
  margin-right: var(--spacing-2);
}

.reports-count {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* ========== 规则列表样式 ========== */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.rule-card {
  transition: box-shadow var(--duration-150) var(--ease-in-out);
}

.rule-card:hover {
  box-shadow: var(--shadow-md);
}

.rule-card .card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.rule-info {
  flex: 1;
  min-width: 0;
}

.rule-info .card-title {
  margin-bottom: var(--spacing-1);
}

.rule-description {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.rule-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.rule-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-3);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.detail-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.detail-item code {
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
  background-color: var(--surface);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  word-break: break-all;
}

/* ========== 报告列表样式 ========== */
.reports-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.report-card {
  transition: box-shadow var(--duration-150) var(--ease-in-out);
}

.report-card:hover {
  box-shadow: var(--shadow-md);
}

.report-card .card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.report-info {
  flex: 1;
  min-width: 0;
}

.report-info .card-title {
  margin-bottom: var(--spacing-1);
}

.report-url {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-family: var(--font-mono);
  word-break: break-all;
}

.report-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.report-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2);
  background-color: var(--background);
  border-radius: var(--radius-sm);
}

.summary-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* ========== 规则编辑器样式 ========== */
.rule-editor-modal .modal-content {
  max-width: 800px;
  width: 90vw;
}

.rule-editor-container {
  min-height: 500px;
}

.form-section {
  margin-bottom: var(--spacing-6);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.form-group-inline {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.form-group-inline .form-label {
  margin-bottom: 0;
  min-width: 120px;
}

/* 高级配置折叠面板 */
.advanced-config {
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.advanced-config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3);
  background-color: var(--surface);
  cursor: pointer;
  transition: background-color var(--duration-150) var(--ease-in-out);
}

.advanced-config-header:hover {
  background-color: var(--surface-hover);
}

.advanced-config-content {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border);
  display: none;
}

.advanced-config.expanded .advanced-config-content {
  display: block;
}

.advanced-config-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: transform var(--duration-150) var(--ease-in-out);
}

.advanced-config.expanded .advanced-config-toggle {
  transform: rotate(180deg);
}

/* ========== 差异查看器样式 ========== */
.diff-viewer-modal .modal-content {
  max-width: 1200px;
  width: 95vw;
  max-height: 90vh;
}

.diff-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--surface);
  border-radius: var(--radius-md);
}

.diff-summary {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.diff-meta {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.diff-html-container {
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  overflow: auto;
  max-height: 500px;
}

.side-by-side-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  height: 500px;
}

.response-panel {
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.response-panel h4 {
  margin: 0;
  padding: var(--spacing-3);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.response-panel pre {
  margin: 0;
  padding: var(--spacing-3);
  overflow: auto;
  height: calc(100% - 48px);
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-relaxed);
}

.raw-data-container h4 {
  margin: var(--spacing-4) 0 var(--spacing-2) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.raw-data-container pre {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  overflow: auto;
  max-height: 300px;
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
}

/* ========== 空状态样式 ========== */
.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--text-secondary);
}

.empty-state p {
  margin: var(--spacing-2) 0;
}

.empty-state .text-muted {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* ========== 依赖指南样式 ========== */
.dependency-guide {
  text-align: center;
}

.dependency-guide h4 {
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

.dependency-guide p {
  margin-bottom: var(--spacing-3);
  color: var(--text-secondary);
}

.code-block {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin: var(--spacing-4) 0;
}

.code-block code {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .api-migration-validator-modal .modal-content {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    border-radius: 0;
  }

  .rule-details,
  .report-summary {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .side-by-side-container {
    grid-template-columns: 1fr;
    height: auto;
  }

  .response-panel {
    height: 300px;
  }

  .rules-toolbar,
  .reports-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .rule-card .card-header,
  .report-card .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .rule-actions,
  .report-actions {
    justify-content: flex-start;
  }
}
