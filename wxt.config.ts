import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
export default defineConfig({
  manifest: {
    name: '服务运营工具集合',
    description: '一个功能丰富的浏览器扩展工具集合，提供各种实用的开发和日常工具',
    version: '1.0.0',
    permissions: [
      'activeTab',
      'storage',
      'tabs',
      'scripting',
      'notifications',
      'downloads',
      'clipboardWrite',
      'cookies',
      'webRequest',
      'webRequestBlocking',
      'clipboardRead'
    ],
    host_permissions: [
      '<all_urls>',
      'https://falcon.op.zuoyebang.cc/*',
      'https://log-search-docker.zuoyebang.cc/*'
    ],
    action: {
      default_title: '服务运营工具集合',
      default_popup: 'popup.html'
    },
    icons: {
      16: '/icon/16.png',
      32: '/icon/32.png',
      48: '/icon/48.png',
      96: '/icon/96.png',
      128: '/icon/128.png'
    },
    web_accessible_resources: [
      {
        resources: ['config/*.json', 'styles/*.css', 'entrypoints/**/*.css'],
        matches: ['<all_urls>']
      }
    ]
  }
});
